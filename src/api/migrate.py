import os
import importlib
from flask import current_app
import logging

from src.connections.connections import Connections

logger = logging.getLogger("conversas_logger")

def run_redis_migrations():
    migrate_path = os.path.join(os.path.dirname(__file__), 'migrations/redis')
    for filename in os.listdir(migrate_path):
        if filename.startswith('version') and filename.endswith('.py'):
            module_name = filename[:-3]
            migration_key = f'migration:{module_name}'

            # Check if the migration has already been executed
            if current_app.redis_client.exists(migration_key):
                logger.info(f'Migration {module_name} already executed.')
                continue

            module = importlib.import_module(f'migrations.redis.{module_name}')
            if hasattr(module, 'migrate') and callable(getattr(module, 'migrate')):
                try:
                    current_app.redis_client.set(migration_key, 'success')
                    getattr(module, 'migrate')()
                    # Store the filename in Redis if migration is successful
                    logger.info(f"Migration {migration_key} executed succesfully")
                except Exception as e:
                    # Optionally log the exception or handle it
                    current_app.redis_client.delete(migration_key)
                    current_app.logger.error(f'Failed to run migration for {module_name}: {e}')

def run_database_migrations():
    """
    Descobre e executa migrações do PostgreSQL, usando uma tabela no próprio
    banco para controlar o histórico de execução.
    """
    connections = Connections.get_instance()
    
    conn = connections.postgres_connection
    
    migrate_path = os.path.join(os.path.dirname(__file__), 'migrations/postgres')
    import_path = 'src.api.migrations.postgres'

    try:
        with conn.cursor() as cursor:
            # Garante que a tabela de histórico exista
            logger.info("Verificando/Criando a tabela de histórico de migrações...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    version VARCHAR(255) PRIMARY KEY,
                    applied_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
                );
            """)
            
            cursor.execute("SELECT version FROM schema_migrations;")
            executed_migrations = {row[0] for row in cursor.fetchall()}

        for filename in sorted(os.listdir(migrate_path)):
            if filename.startswith('version') and filename.endswith('.py'):
                module_name = filename[:-3]
                if module_name in executed_migrations:
                    logger.info(f'Migration {module_name} já executada.')
                    continue

                try:
                    module_path = f'{import_path}.{module_name}'
                    logger.info(f"Executando migração: {module_path}")
                    module = importlib.import_module(module_path)
                    
                    if hasattr(module, 'migrate') and callable(getattr(module, 'migrate')):
                        getattr(module, 'migrate')()

                        with conn.cursor() as cursor:
                            cursor.execute(
                                "INSERT INTO schema_migrations (version) VALUES (%s);",
                                (module_name,)
                            )
                        
                        conn.commit()
                        logger.info(f"Migration {module_name} executada e registrada com sucesso.")
                
                except Exception as e:
                    logger.error(f'Falha ao executar a migração {module_name}: {e}', exc_info=True)
                    conn.rollback()

    except Exception as e:
        logger.error(f"Um erro crítico ocorreu durante o processo de migração: {e}", exc_info=True)
        if conn:
            conn.rollback()

    finally:
        if conn:
            conn.close()